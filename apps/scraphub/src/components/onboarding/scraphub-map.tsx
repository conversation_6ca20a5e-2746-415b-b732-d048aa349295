interface ScraphubMapProps {
  selectedLocationCoordinates: {
    latitude: number;
    longitude: number;
  };
}

export function ScraphubMap({ selectedLocationCoordinates }: ScraphubMapProps) {
  const { latitude, longitude } = selectedLocationCoordinates;

  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium text-gray-700">Selected Location</h4>
      <div className="h-64 w-full rounded-lg border bg-gray-50 p-4">
        <div className="flex h-full flex-col items-center justify-center space-y-2 text-center">
          <div className="text-2xl">📍</div>
          <div className="text-sm text-gray-600">
            Latitude: {latitude.toFixed(6)}
          </div>
          <div className="text-sm text-gray-600">
            Longitude: {longitude.toFixed(6)}
          </div>
          <div className="text-xs text-gray-500">
            Click on the map above to select a different location
          </div>
        </div>
      </div>
    </div>
  );
}
