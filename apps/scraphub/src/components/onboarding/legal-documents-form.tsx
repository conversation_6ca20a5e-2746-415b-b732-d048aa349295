import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";

import type { LegalDocumentsForm, UploadState } from "./types";
import { UploadDropzone } from "~/hooks/use-uploadthing";
import { useTRPC } from "~/trpc/react";
import { legalDocumentsSchema } from "./types";

interface LegalDocumentsFormProps {
  onSubmit: (data: LegalDocumentsForm) => Promise<void>;
  onBack?: () => void;
  isSubmitting: boolean;
  initialData?: Partial<LegalDocumentsForm>;
}

export function LegalDocumentsForm({
  onSubmit,
  onBack,
  isSubmitting,
  initialData,
}: LegalDocumentsFormProps) {
  const [uploadStates, setUploadStates] = useState<UploadState>({});
  const trpc = useTRPC();

  const form = useForm<LegalDocumentsForm>({
    resolver: zodResolver(legalDocumentsSchema),
    defaultValues: {
      cinNumber: "",
      cinCertificateKey: "",
      gstNumber: "",
      gstCertificateKey: "",
      panNumber: "",
      panCertificateKey: "",
    },
  });

  // Prefill form with initial data when available
  useEffect(() => {
    if (initialData) {
      form.reset({
        cinNumber: initialData.cinNumber || "",
        cinCertificateKey: initialData.cinCertificateKey || "",
        gstNumber: initialData.gstNumber || "",
        gstCertificateKey: initialData.gstCertificateKey || "",
        panNumber: initialData.panNumber || "",
        panCertificateKey: initialData.panCertificateKey || "",
      });
    }
  }, [initialData, form]);

  const deleteFileMutation = useMutation(
    trpc.utils.deleteUploadthingFileUsingFileKey.mutationOptions(),
  );

  const handleFileUpload = async (
    fieldName: keyof LegalDocumentsForm,
    newFileKey: string,
    oldFileKey?: string,
  ) => {
    // Delete old file if it exists
    if (oldFileKey) {
      try {
        await deleteFileMutation.mutateAsync({ fileKey: oldFileKey });
      } catch (error) {
        console.error(`Failed to delete old file: ${oldFileKey}`, error);
        toast.error("Failed to delete old file");
      }
    }

    // Update form with new file key
    form.setValue(fieldName, newFileKey);
  };

  const isAnyFileUploading = Object.values(uploadStates).some(
    (isUploading) => isUploading,
  );

  const handleSubmit = async (data: LegalDocumentsForm) => {
    if (isAnyFileUploading) {
      toast.error("Please wait for all files to finish uploading");
      return;
    }
    await onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="cinNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>CIN Number *</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter CIN number"
                  value={field.value || ""}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="cinCertificateKey"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Certificate of Incorporation *</FormLabel>
              <FormControl>
                <UploadDropzone
                  endpoint="documentUploader"
                  onClientUploadComplete={(res) => {
                    if (res && res.length > 0) {
                      const newFileKey = res[0]?.key;
                      if (newFileKey) {
                        handleFileUpload(
                          "cinCertificateKey",
                          newFileKey,
                          field.value,
                        );
                      }
                    }
                    setUploadStates((prev) => ({
                      ...prev,
                      cinCertificate: false,
                    }));
                  }}
                  onUploadBegin={() => {
                    setUploadStates((prev) => ({
                      ...prev,
                      cinCertificate: true,
                    }));
                  }}
                  config={{
                    mode: "auto",
                  }}
                  onUploadError={(error) => {
                    toast.error(error.message);
                    setUploadStates((prev) => ({
                      ...prev,
                      cinCertificate: false,
                    }));
                  }}
                  appearance={{
                    container: "w-full",
                    allowedContent: "text-muted-foreground text-xs",
                  }}
                  content={{
                    allowedContent: "PDF, JPEG, PNG (max 4MB)",
                  }}
                />
              </FormControl>
              {field.value && (
                <div className="mt-2 flex items-center gap-2 rounded-md border bg-gray-50 p-2">
                  <span className="text-sm text-gray-600">Uploaded:</span>
                  <a
                    href={`https://sehqs1pdr6.ufs.sh/f/${field.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    View Document
                  </a>
                </div>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="gstNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>GST Number *</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter GST number"
                  value={field.value || ""}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="gstCertificateKey"
          render={({ field }) => (
            <FormItem>
              <FormLabel>GST RC *</FormLabel>
              <FormControl>
                <UploadDropzone
                  endpoint="documentUploader"
                  onClientUploadComplete={(res) => {
                    if (res && res.length > 0) {
                      const newFileKey = res[0]?.key;
                      if (newFileKey) {
                        handleFileUpload(
                          "gstCertificateKey",
                          newFileKey,
                          field.value,
                        );
                      }
                    }
                    setUploadStates((prev) => ({
                      ...prev,
                      gstCertificate: false,
                    }));
                  }}
                  onUploadBegin={() => {
                    setUploadStates((prev) => ({
                      ...prev,
                      gstCertificate: true,
                    }));
                  }}
                  config={{
                    mode: "auto",
                  }}
                  onUploadError={(error) => {
                    toast.error(error.message);
                    setUploadStates((prev) => ({
                      ...prev,
                      gstCertificate: false,
                    }));
                  }}
                  appearance={{
                    container: "w-full",
                    allowedContent: "text-muted-foreground text-xs",
                  }}
                  content={{
                    allowedContent: "PDF, JPEG, PNG (max 4MB)",
                  }}
                />
              </FormControl>
              {field.value && (
                <div className="mt-2 flex items-center gap-2 rounded-md border bg-gray-50 p-2">
                  <span className="text-sm text-gray-600">Uploaded:</span>
                  <a
                    href={`https://sehqs1pdr6.ufs.sh/f/${field.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    View Document
                  </a>
                </div>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="panNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>PAN Number *</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter PAN number"
                  value={field.value || ""}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="panCertificateKey"
          render={({ field }) => (
            <FormItem>
              <FormLabel>PAN Copy *</FormLabel>
              <FormControl>
                <UploadDropzone
                  endpoint="documentUploader"
                  onClientUploadComplete={(res) => {
                    if (res && res.length > 0) {
                      const newFileKey = res[0]?.key;
                      if (newFileKey) {
                        handleFileUpload(
                          "panCertificateKey",
                          newFileKey,
                          field.value,
                        );
                      }
                    }
                    setUploadStates((prev) => ({
                      ...prev,
                      panCertificate: false,
                    }));
                  }}
                  onUploadBegin={() => {
                    setUploadStates((prev) => ({
                      ...prev,
                      panCertificate: true,
                    }));
                  }}
                  config={{
                    mode: "auto",
                  }}
                  onUploadError={(error) => {
                    toast.error(error.message);
                    setUploadStates((prev) => ({
                      ...prev,
                      panCertificate: false,
                    }));
                  }}
                  appearance={{
                    container: "w-full",
                    allowedContent: "text-muted-foreground text-xs",
                  }}
                  content={{
                    allowedContent: "PDF, JPEG, PNG (max 4MB)",
                  }}
                />
              </FormControl>
              {field.value && (
                <div className="mt-2 flex items-center gap-2 rounded-md border bg-gray-50 p-2">
                  <span className="text-sm text-gray-600">Uploaded:</span>
                  <a
                    href={`https://sehqs1pdr6.ufs.sh/f/${field.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    View Document
                  </a>
                </div>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-center gap-4 pt-4">
          {onBack && (
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              disabled={isSubmitting || isAnyFileUploading}
            >
              Back
            </Button>
          )}
          <Button
            type="submit"
            disabled={isSubmitting || isAnyFileUploading}
            className="bg-teal-600 px-8 hover:bg-teal-700"
          >
            {isSubmitting
              ? "Saving..."
              : isAnyFileUploading
                ? "Uploading..."
                : "Save & Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
