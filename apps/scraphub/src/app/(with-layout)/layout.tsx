import "@acme/ui/globals.css";

import { Poppin<PERSON> } from "next/font/google";
import { redirect } from "next/navigation";

import { Toaster } from "@acme/ui/components/ui/sonner";

import { UploadThingProviderWrapper } from "~/components/providers/uploadthing-provider";
import { AppSidebar } from "~/components/shared/app-sidebar";
import SidebarProvider from "~/components/shared/sidebar-provider";
import { TRPCReactProvider } from "~/trpc/react";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const queryClient = getQueryClient();
  const onboardingStatus = await queryClient.fetchQuery(
    trpc.auth.getOnboardingStatus.queryOptions(),
  );

  // If onboarding is not completed, don't render the layout
  if (!onboardingStatus.signupCompleted) {
    redirect("/onboarding");
  }

  return (
    <html
      lang="en"
      className={`${poppins.className} min-h-screen text-foreground antialiased`}
    >
      <body>
        <TRPCReactProvider>
          <UploadThingProviderWrapper>
            <HydrateClient>
              <SidebarProvider>
                <div className="flex h-screen">
                  <AppSidebar />
                  <main className="flex-1 overflow-y-auto">{children}</main>
                </div>
                <Toaster />
              </SidebarProvider>
            </HydrateClient>
          </UploadThingProviderWrapper>
        </TRPCReactProvider>
      </body>
    </html>
  );
}
