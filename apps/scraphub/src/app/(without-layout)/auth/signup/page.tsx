"use client";

import type { FieldErrors } from "react-hook-form";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { Button } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";

import { authClient } from "~/server/auth/client";
import { useTRPC } from "~/trpc/react";

const signupSchema = z.object({
  name: z.string().min(1),
  email: z.string().email(),
  password: z.string().min(8),
});

export default function SignupPage() {
  const form = useForm<z.infer<typeof signupSchema>>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
    },
  });

  const router = useRouter();
  const trpc = useTRPC();

  const signupMutation = useMutation(
    trpc.auth.initialSignup.mutationOptions({
      onSuccess: () => {
        toast.success(
          "Account created successfully! Please check your email to verify your account.",
        );
        router.push("/onboarding");
      },
      onError: (error) => {
        toast.error(error.message || "Signup failed");
      },
    }),
  );

  const handleSubmit = async (data: z.infer<typeof signupSchema>) => {
    try {
      await authClient.signUp.email(
        {
          email: data.email,
          password: data.password,
          name: data.name,
          callbackURL: "/onboarding",
        },
        {
          onSuccess: () => {
            toast.success(
              "Account created successfully! Please check your email to verify your account.",
            );
          },
        },
      );
    } catch (error) {
      console.error("Signup error:", error);
    }
  };

  const handleError = (errors: FieldErrors<z.infer<typeof signupSchema>>) => {
    console.log(errors);
    toast.error("Signup fields are invalid");
  };

  // if (isSuccess) {
  //   return (
  //     <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
  //       <Card className="w-full max-w-md">
  //         <CardHeader className="text-center">
  //           <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
  //             <CheckCircle className="h-8 w-8 text-green-600" />
  //           </div>
  //           <CardTitle className="text-xl font-semibold text-gray-900">
  //             Account Created Successfully!
  //           </CardTitle>
  //         </CardHeader>
  //         <CardContent className="space-y-4 text-center">
  //           <p className="text-gray-600">
  //             We've sent a verification email to{" "}
  //             <strong>{formData.email}</strong>. Please check your inbox and
  //             click the verification link to complete your account setup.
  //           </p>
  //           <div className="space-y-2">
  //             <p className="text-sm text-gray-500">
  //               After verifying your email, you'll be able to:
  //             </p>
  //             <ul className="space-y-1 text-sm text-gray-600">
  //               <li>• Complete your organization profile</li>
  //               <li>• Upload required documents</li>
  //               <li>• Set up your business details</li>
  //             </ul>
  //           </div>
  //           <div className="pt-4">
  //             <Link
  //               href="/auth/login"
  //               className="font-medium text-teal-600 hover:text-teal-500"
  //             >
  //               ← Back to Login
  //             </Link>
  //           </div>
  //         </CardContent>
  //       </Card>
  //     </div>
  //   );
  // }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <Card className="w-full md:w-2/3 lg:w-1/2">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-gray-900">
            Create Your Account
          </CardTitle>
          <p className="text-gray-600">
            Start your ScrapHub organization journey
          </p>
        </CardHeader>

        <CardContent>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit, handleError)}
              className="flex flex-col gap-2"
            >
              <div className="flex flex-row gap-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="flex-auto">
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter your name" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex flex-row gap-2">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="flex-auto">
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter your email address"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem className="flex-auto">
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter your password"
                          type="password"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-teal-600 hover:bg-teal-700"
                disabled={signupMutation.isPending}
              >
                {signupMutation.isPending
                  ? "Creating Account..."
                  : "Create Account"}
              </Button>

              <div className="text-center text-sm text-gray-600">
                Already have an account?{" "}
                <Link
                  href="/auth/login"
                  className="font-medium text-teal-600 hover:text-teal-500"
                >
                  Login here
                </Link>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
