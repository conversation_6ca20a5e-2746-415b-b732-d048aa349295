"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";

import type {
  AddressForm,
  DirectorInfoForm,
  LegalDocumentsForm,
  OrganizationDetailsForm,
} from "~/components/onboarding/types";
import {
  AddressFormComponent,
  DirectorInfoFormComponent,
  LegalDocumentsFormComponent,
  LoadingSpinner,
  OnboardingSteps,
  OrganizationDetailsFormComponent,
} from "~/components/onboarding";
import { useTRPC } from "~/trpc/react";

// Default coordinates (Delhi, India)
const DEFAULT_COORDINATES = { latitude: 28.6139, longitude: 77.209 };

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const trpc = useTRPC();

  // Store form data for each step
  const [stepData, setStepData] = useState({
    organizationDetails: {} as Partial<OrganizationDetailsForm>,
    legalDocuments: {} as Partial<LegalDocumentsForm>,
    directorInfo: {} as Partial<DirectorInfoForm>,
    address: {} as Partial<AddressForm>,
  });

  // Get current onboarding status
  const { data: onboardingStatus, isLoading: isLoadingStatus } = useQuery(
    trpc.auth.getOnboardingStatus.queryOptions(),
  );

  // Mutations for each step
  const saveOrganizationDetailsMutation = useMutation(
    trpc.auth.saveOrganizationDetails.mutationOptions({
      onSuccess: () => {
        toast.success("Organization details saved successfully!");
        setCurrentStep(2);
      },
      onError: (error) => {
        toast.error(error.message || "Failed to save organization details");
      },
    }),
  );

  const saveLegalDocumentsMutation = useMutation(
    trpc.auth.saveLegalDocuments.mutationOptions({
      onSuccess: () => {
        toast.success("Legal documents saved successfully!");
        setCurrentStep(3);
      },
      onError: (error) => {
        toast.error(error.message || "Failed to save legal documents");
      },
    }),
  );

  const saveDirectorInfoMutation = useMutation(
    trpc.auth.saveDirectorInfo.mutationOptions({
      onSuccess: () => {
        toast.success("Director information saved successfully!");
        setCurrentStep(4);
      },
      onError: (error) => {
        toast.error(error.message || "Failed to save director information");
      },
    }),
  );

  // Step 4: Save address information
  const saveAddressMutation = useMutation(
    trpc.auth.saveAddress.mutationOptions({
      onSuccess: () => {
        toast.success("Onboarding completed successfully!");
        router.push("/");
      },
      onError: (error) => {
        toast.error(error.message || "Failed to save address information");
      },
    }),
  );

  // Redirect if onboarding is already completed
  useEffect(() => {
    if (onboardingStatus?.signupCompleted) {
      router.push("/");
    }
  }, [onboardingStatus, router]);

  // Set current step based on onboarding status
  useEffect(() => {
    if (onboardingStatus?.onboardingStep) {
      switch (onboardingStatus.onboardingStep) {
        case "EMAIL_VERIFIED":
          setCurrentStep(1);
          break;
        case "ORGANIZATION_DETAILS":
          setCurrentStep(2);
          break;
        case "LEGAL_DOCUMENTS":
          setCurrentStep(3);
          break;
        case "DIRECTOR_INFO":
          setCurrentStep(4);
          break;
        case "ADDRESS":
          setCurrentStep(4);
          break;
        case "COMPLETED":
          router.push("/");
          break;
      }
    }
  }, [onboardingStatus, router]);

  // Navigation functions
  const goToStep = (step: number) => {
    if (step >= 1 && step <= 4) {
      setCurrentStep(step);
    }
  };

  const goBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goForward = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  // Form submission handlers
  const handleOrganizationDetailsSubmit = async (
    data: OrganizationDetailsForm,
  ) => {
    setIsSubmitting(true);
    try {
      // Save data locally
      setStepData((prev) => ({
        ...prev,
        organizationDetails: data,
      }));

      await saveOrganizationDetailsMutation.mutateAsync({
        organizationName: data.organizationName,
        organizationType: data.organizationType as any,
        registeredOfficeAddress: data.registeredOfficeAddress,
        communicationAddress: data.communicationAddress,
      });
    } catch (error) {
      console.error("Step 1 error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLegalDocumentsSubmit = async (data: LegalDocumentsForm) => {
    setIsSubmitting(true);
    try {
      // Save data locally
      setStepData((prev) => ({
        ...prev,
        legalDocuments: data,
      }));

      await saveLegalDocumentsMutation.mutateAsync({
        cinNumber: data.cinNumber,
        cinCertificateKey: data.cinCertificateKey,
        gstNumber: data.gstNumber,
        gstCertificateKey: data.gstCertificateKey,
        panNumber: data.panNumber,
        panCertificateKey: data.panCertificateKey,
      });
    } catch (error) {
      console.error("Step 2 error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDirectorInfoSubmit = async (data: DirectorInfoForm) => {
    setIsSubmitting(true);
    try {
      // Save data locally
      setStepData((prev) => ({
        ...prev,
        directorInfo: data,
      }));

      await saveDirectorInfoMutation.mutateAsync({
        directorName: data.directorName,
        directorAadharKey: data.directorAadharKey,
      });
    } catch (error) {
      console.error("Step 3 error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddressSubmit = async (data: AddressForm) => {
    setIsSubmitting(true);
    try {
      // Save data locally
      setStepData((prev) => ({
        ...prev,
        address: data,
      }));

      await saveAddressMutation.mutateAsync({
        display: data.display,
        street: data.street,
        city: data.city,
        state: data.state,
        country: data.country,
        postalCode: data.postalCode,
        coordinates: data.coordinates,
        localAddress: data.localAddress,
        landmark: data.landmark,
      });
    } catch (error) {
      console.error("Step 4 error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCurrentStep = () => {
    if (!onboardingStatus) return null;

    switch (currentStep) {
      case 1:
        return (
          onboardingStatus.organizationDetails && (
            <OrganizationDetailsFormComponent
              onSubmit={handleOrganizationDetailsSubmit}
              isSubmitting={isSubmitting}
              initialData={{
                organizationName:
                  onboardingStatus.organizationDetails.organizationName || "",
                organizationType:
                  onboardingStatus.organizationDetails.organizationType || "",
                registeredOfficeAddress:
                  onboardingStatus.organizationDetails
                    .registeredOfficeAddress || "",
                communicationAddress:
                  onboardingStatus.organizationDetails.communicationAddress ||
                  "",
              }}
            />
          )
        );
      case 2:
        return (
          onboardingStatus.legalDocuments && (
            <LegalDocumentsFormComponent
              onSubmit={handleLegalDocumentsSubmit}
              onBack={goBack}
              isSubmitting={isSubmitting}
              initialData={{
                cinNumber: onboardingStatus.legalDocuments.cinNumber || "",
                cinCertificateKey:
                  onboardingStatus.legalDocuments.cinCertificateKey || "",
                gstNumber: onboardingStatus.legalDocuments.gstNumber || "",
                gstCertificateKey:
                  onboardingStatus.legalDocuments.gstCertificateKey || "",
                panNumber: onboardingStatus.legalDocuments.panNumber || "",
                panCertificateKey:
                  onboardingStatus.legalDocuments.panCertificateKey || "",
              }}
            />
          )
        );
      case 3:
        return (
          onboardingStatus.directorInfo && (
            <DirectorInfoFormComponent
              onSubmit={handleDirectorInfoSubmit}
              onBack={goBack}
              isSubmitting={isSubmitting}
              initialData={{
                directorName: onboardingStatus.directorInfo.directorName || "",
                directorAadharKey:
                  onboardingStatus.directorInfo.directorAadharKey || "",
              }}
            />
          )
        );
      case 4:
        return (
          onboardingStatus.address && (
            <AddressFormComponent
              onSubmit={handleAddressSubmit}
              onBack={goBack}
              isSubmitting={isSubmitting}
              initialData={{
                display: onboardingStatus.address.display || "",
                street: onboardingStatus.address.street || "",
                city: onboardingStatus.address.city || "",
                state: onboardingStatus.address.state || "",
                country: onboardingStatus.address.country || "",
                postalCode: onboardingStatus.address.postalCode || "",
                coordinates:
                  onboardingStatus.address.coordinates || DEFAULT_COORDINATES,
                localAddress: onboardingStatus.address.localAddress || "",
                landmark: onboardingStatus.address.landmark || "",
              }}
            />
          )
        );
      default:
        return null;
    }
  };

  const getCurrentStepTitle = () => {
    switch (currentStep) {
      case 1:
        return "Organization Details";
      case 2:
        return "Legal Documents";
      case 3:
        return "Director Information";
      case 4:
        return "Scraphub Address";
      default:
        return "Onboarding";
    }
  };

  if (isLoadingStatus) {
    return <LoadingSpinner message="Loading onboarding status..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="mx-auto max-w-4xl">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold">
              Complete Your Organization Setup
            </CardTitle>
            <p className="text-gray-600">
              Let's get your organization fully configured
            </p>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Progress Steps */}
            <OnboardingSteps currentStep={currentStep} />

            {/* Step Content */}
            <div className="min-h-[400px]">
              <h3 className="mb-4 text-lg font-semibold">
                {getCurrentStepTitle()}
              </h3>
              {renderCurrentStep()}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
