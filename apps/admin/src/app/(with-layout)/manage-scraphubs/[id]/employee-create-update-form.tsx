"use client";

import type { z } from "zod";
import { useEffect } from "react";
import { useParams } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";

import { useSheet } from "~/hooks/use-sheet";
import { employeeParamName } from "~/lib/constants";
import { ScraphubEmployee } from "~/lib/zod-schema";
import { useTRPC } from "~/trpc/react";

type EmployeeFormValues = z.infer<typeof ScraphubEmployee>;

const EmployeeCreateUpdateForm = () => {
  const { closeSheet, paramValue } = useSheet(employeeParamName);
  const params = useParams();
  const scraphubId = params.id as string;
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const isUpdateMode = !!paramValue;

  const form = useForm<EmployeeFormValues>({
    resolver: zodResolver(ScraphubEmployee),
    defaultValues: {
      name: "",
      email: "",
    },
  });

  // Load employee data when in edit mode using skipToken pattern
  const { data: employee, isLoading: isEmployeeLoading } = useQuery(
    trpc.scraphub.getEmployeeById.queryOptions(
      paramValue ? { employeeId: paramValue } : skipToken,
    ),
  );

  const { mutate: createEmployee, isPending: isPendingCreateEmployee } =
    useMutation(
      trpc.scraphub.createEmployee.mutationOptions({
        onSuccess: (opts) => {
          toast.success(opts.message);
          form.reset();
          closeSheet();
          // Invalidate relevant queries
          void queryClient.invalidateQueries({
            queryKey: trpc.scraphub.getScraphubById.queryKey({ scraphubId }),
          });
          void queryClient.invalidateQueries({
            queryKey: trpc.scraphub.getAllEmployees.queryKey({ scraphubId }),
          });
        },
        onError: (error) => {
          toast.error(error.message);
        },
      }),
    );

  const { mutate: updateEmployee, isPending: isPendingUpdateEmployee } =
    useMutation(
      trpc.scraphub.updateEmployee.mutationOptions({
        onSuccess: (opts) => {
          toast.success(opts.message);
          form.reset();
          closeSheet();
          void queryClient.invalidateQueries({
            queryKey: trpc.scraphub.getAllEmployees.queryKey({ scraphubId }),
          });
          void queryClient.invalidateQueries({
            queryKey: trpc.scraphub.getEmployeeById.queryKey({
              employeeId: paramValue ?? "",
            }),
          });
        },
        onError: (error) => {
          toast.error(error.message);
        },
      }),
    );

  // Reset form when employee data is loaded or mode changes
  useEffect(() => {
    if (isUpdateMode) {
      if (employee) {
        form.reset({
          name: employee.name,
          email: employee.email,
        });
      }
    } else {
      form.reset({
        name: "",
        email: "",
      });
    }
  }, [employee, form, isUpdateMode]);

  const onSubmit = (data: EmployeeFormValues) => {
    if (isPendingCreateEmployee || isPendingUpdateEmployee) return;

    if (isUpdateMode && paramValue) {
      updateEmployee({
        ...data,
        employeeId: paramValue,
      });
    } else {
      createEmployee({
        ...data,
        scraphubId,
      });
    }
  };

  if (isEmployeeLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading employee data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-sm text-muted-foreground">
        {isUpdateMode
          ? "Update employee information. Changes will be saved immediately."
          : "Create a new employee account. System will generate credentials and send them via email."}
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter first name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="Enter email address"
                    {...field}
                  />
                </FormControl>
                {!isUpdateMode && (
                  <FormDescription>
                    Login credentials will be sent to this email.
                  </FormDescription>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex gap-2 pt-4">
            <Button
              type="submit"
              disabled={isPendingCreateEmployee || isPendingUpdateEmployee}
              className="flex-1"
            >
              {isPendingCreateEmployee || isPendingUpdateEmployee ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isUpdateMode ? "Updating..." : "Creating..."}
                </>
              ) : isUpdateMode ? (
                "Update Employee"
              ) : (
                "Create Employee"
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                form.reset();
                closeSheet();
              }}
              disabled={isPendingCreateEmployee || isPendingUpdateEmployee}
            >
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default EmployeeCreateUpdateForm;
