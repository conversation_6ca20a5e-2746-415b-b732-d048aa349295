import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import bcrypt from "bcryptjs";
import { and, eq, ilike, isNull, or } from "drizzle-orm";
import { z } from "zod";

import {
  kabadiwala,
  scraphub,
  scraphubAddress,
  scraphubEmployee,
  scraphubEmployeeAccount,
} from "@acme/db/schema";
import { sendEmail } from "@acme/mail";
import { tryCatch } from "@acme/validators/utils";

import { ScraphubEmployee, ScraphubSchema } from "~/lib/zod-schema";
import { protectedProcedure } from "../trpc";

export const scraphubRouter = {
  getAllScraphubs: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: scraphub.id,
          name: scraphub.name,
          phoneNumber: scraphub.phoneNumber,
          address: {
            id: scraphubAddress.id,
            display: scraphubAddress.display,
            street: scraphubAddress.street,
            city: scraphubAddress.city,
            state: scraphubAddress.state,
            country: scraphubAddress.country,
            postalCode: scraphubAddress.postalCode,
            coordinates: scraphubAddress.coordinates,
            localAddress: scraphubAddress.localAddress,
            landmark: scraphubAddress.landmark,
            createdAt: scraphubAddress.createdAt,
          },
        })
        .from(scraphub)
        .leftJoin(scraphubAddress, eq(scraphub.id, scraphubAddress.scraphubId)),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch scraphubs",
      });
    }

    return data;
  }),

  getScraphubById: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: scraphub.id,
            name: scraphub.name,
            address: {
              id: scraphubAddress.id,
              display: scraphubAddress.display,
              street: scraphubAddress.street,
              city: scraphubAddress.city,
              state: scraphubAddress.state,
              country: scraphubAddress.country,
              postalCode: scraphubAddress.postalCode,
              coordinates: scraphubAddress.coordinates,
              localAddress: scraphubAddress.localAddress,
              landmark: scraphubAddress.landmark,
            },
            phoneNumber: scraphub.phoneNumber,
          })
          .from(scraphub)
          .leftJoin(
            scraphubAddress,
            eq(scraphub.id, scraphubAddress.scraphubId),
          )
          .where(eq(scraphub.id, input.scraphubId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch scraphub",
        });
      }

      if (!data[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Scraphub not found",
        });
      }

      return data[0];
    }),

  createScraphub: protectedProcedure
    .input(ScraphubSchema)
    .mutation(async ({ input, ctx }) => {
      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const [scraphubData] = await tx
            .insert(scraphub)
            .values({
              name: input.name,
              phoneNumber: input.phoneNumber,
            })
            .returning({ id: scraphub.id });

          if (!scraphubData) {
            throw new Error("Failed to create scraphub");
          }

          await tx.insert(scraphubAddress).values({
            display: input.address.display,
            street: input.address.street,
            city: input.address.city,
            state: input.address.state,
            country: input.address.country,
            postalCode: input.address.postalCode,
            coordinates: input.address.coordinates,
            localAddress: input.address.localAddress,
            landmark: input.address.landmark,
            scraphubId: scraphubData.id,
          });
        }),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to create scraphub",
        });
      }

      return {
        message: "Scraphub created successfully",
      };
    }),

  updateScraphub: protectedProcedure
    .input(
      ScraphubSchema.extend({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          await tx
            .update(scraphub)
            .set({
              name: input.name,
              phoneNumber: input.phoneNumber,
            })
            .where(eq(scraphub.id, input.scraphubId));

          await tx
            .update(scraphubAddress)
            .set({
              display: input.address.display,
              street: input.address.street,
              city: input.address.city,
              state: input.address.state,
              country: input.address.country,
              postalCode: input.address.postalCode,
              coordinates: input.address.coordinates,
              localAddress: input.address.localAddress,
              landmark: input.address.landmark,
              updatedAt: new Date(),
            })
            .where(eq(scraphubAddress.scraphubId, input.scraphubId));
        }),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to update scraphub",
        });
      }

      return {
        message: "Scraphub updated successfully",
      };
    }),

  deleteScraphub: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db.delete(scraphub).where(eq(scraphub.id, input.scraphubId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete scraphub",
        });
      }

      return {
        message: "Scraphub deleted successfully",
      };
    }),

  createEmployee: protectedProcedure
    .input(
      ScraphubEmployee.extend({
        scraphubId: z.string().nonempty({ message: "Scraphub IDis required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if email already exists
      const { data: existingEmployee, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: scraphubEmployee.id })
          .from(scraphubEmployee)
          .where(eq(scraphubEmployee.email, input.email)),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing employee",
        });
      }

      if (existingEmployee[0]) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already registered",
        });
      }

      // Generate a random password
      const generatedPassword = Math.random().toString(36).slice(-8);

      // Hash the password
      const hashedPassword = await bcrypt.hash(generatedPassword, 10);

      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const [scraphubEmployeeRes] = await tx
            .insert(scraphubEmployee)
            .values({
              scraphubId: input.scraphubId,
              name: input.name,
              email: input.email,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning();

          if (!scraphubEmployeeRes) {
            throw new Error("Failed to create scraphub employee");
          }

          await tx.insert(scraphubEmployeeAccount).values({
            scraphubEmployeeId: scraphubEmployeeRes.id,
            accountId: scraphubEmployeeRes.id,
            password: hashedPassword,
            providerId: "credential",
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to create employee",
        });
      }

      await sendEmail({
        to: input.email,
        subject: "Welcome to Scraplo",
        content: `Hello ${input.firstName},\n\nYour account has been created successfully. Here are your credentials:\n\nEmail: ${input.email}\nPassword: ${generatedPassword}\n\nPlease log in to the portal and change your password.\n\nThank you!`,
      });

      return {
        message: "Employee created successfully. Credentials sent to email.",
      };
    }),

  getAllEmployees: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: employees, err: employeesErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findMany({
          where: eq(scraphubEmployee.scraphubId, input.scraphubId),
        }),
      );

      if (employeesErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employees",
        });
      }

      return employees;
    }),

  getEmployeeById: protectedProcedure
    .input(
      z.object({
        employeeId: z.string().nonempty({ message: "Employee ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: employee, err: employeeErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, input.employeeId),
        }),
      );

      if (employeeErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employee",
        });
      }

      if (!employee) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employee not found",
        });
      }

      return employee;
    }),

  updateEmployee: protectedProcedure
    .input(
      ScraphubEmployee.extend({
        employeeId: z.string().nonempty({ message: "Employee ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if email already exists for other employees
      const { data: existingEmployee, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: scraphubEmployee.id })
          .from(scraphubEmployee)
          .where(eq(scraphubEmployee.email, input.email)),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing employee",
        });
      }

      if (existingEmployee[0] && existingEmployee[0].id !== input.employeeId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already registered",
        });
      }

      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(scraphubEmployee)
          .set({
            name: input.name,
            email: input.email,
            updatedAt: new Date(),
          })
          .where(eq(scraphubEmployee.id, input.employeeId)),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: updateErr.message || "Failed to update employee",
        });
      }

      return {
        message: "Employee updated successfully",
      };
    }),

  searchKabadiwalas: protectedProcedure
    .input(
      z.object({
        query: z.string().min(1, { message: "Search query is required" }),
        scraphubId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.query.kabadiwala.findMany({
          columns: {
            id: true,
            name: true,
            image: true,
            phoneNumber: true,
            phoneNumberVerified: true,
            averageRating: true,
            scraphubId: true,
            createdAt: true,
          },
          where: and(
            or(isNull(kabadiwala.isBlocked), eq(kabadiwala.isBlocked, false)),
            or(
              eq(kabadiwala.scraphubId, input.scraphubId),
              isNull(kabadiwala.scraphubId),
            ),
            ilike(kabadiwala.name, `%${input.query}%`),
          ),
          limit: 10,
        }),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Failed to search kabadiwalas",
        });
      }
      return { data };
    }),

  getAllAssociatedKabadiwalas: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: kabadiwalas, err: kabadiwalasErr } = await tryCatch(
        ctx.db.query.kabadiwala.findMany({
          where: eq(kabadiwala.scraphubId, input.scraphubId),
          columns: {
            id: true,
            name: true,
            phoneNumber: true,
            averageRating: true,
            image: true,
            isOnDuty: true,
            isBlocked: true,
          },
        }),
      );

      if (kabadiwalasErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwalas",
        });
      }

      return kabadiwalas;
    }),

  assignKabadiwala: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            scraphubId: input.scraphubId,
          })
          .where(eq(kabadiwala.id, input.kabadiwalaId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to assign kabadiwala",
        });
      }

      return {
        message: "Kabadiwala assigned successfully",
      };
    }),

  removeAssignedKabadiwala: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            scraphubId: null,
          })
          .where(eq(kabadiwala.id, input.kabadiwalaId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to remove kabadiwala assignment",
        });
      }

      return {
        message: "Kabadiwala assignment removed successfully",
      };
    }),
} satisfies TRPCRouterRecord;
