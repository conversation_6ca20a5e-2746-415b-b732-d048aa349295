{"name": "scraplog-landing", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .cache .next .turbo node_modules", "dev": "pnpm with-env next dev -p 3000 --turbopack", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/db": "workspace:*", "@acme/ui": "workspace:*", "@hookform/resolvers": "^4.1.3", "@t3-oss/env-nextjs": "^0.13.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "catalog:", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.477.0", "next": "catalog:", "next-themes": "^0.4.4", "react": "catalog:react19", "react-day-picker": "^9", "react-dom": "catalog:react19", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tailwind-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/node": "^22.14.1", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "postcss": "^8.5.3", "prettier": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}}